apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
    control-plane: controller-manager
  name: etcd-k8s-operator-system
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: etcdbackups.etcd.etcd.io
spec:
  group: etcd.etcd.io
  names:
    kind: EtcdBackup
    listKind: EtcdBackupList
    plural: etcdbackups
    shortNames:
    - etcdbackup
    singular: etcdbackup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .spec.clusterName
      name: Cluster
      type: string
    - jsonPath: .spec.storageType
      name: Storage
      type: string
    - jsonPath: .status.backupSize
      name: Size
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdBackup is the Schema for the etcdbackups API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: EtcdBackupSpec defines the desired state of EtcdBackup
            properties:
              clusterName:
                description: ClusterName is the name of the EtcdCluster to backup
                type: string
              clusterNamespace:
                description: ClusterNamespace is the namespace of the EtcdCluster
                type: string
              compression:
                description: Compression indicates whether to compress the backup
                type: boolean
              retentionPolicy:
                description: RetentionPolicy defines backup retention
                properties:
                  maxAge:
                    description: MaxAge is the maximum age of backups to retain
                    type: string
                  maxBackups:
                    description: MaxBackups is the maximum number of backups to retain
                    format: int32
                    type: integer
                type: object
              s3:
                description: S3 configuration for S3 storage
                properties:
                  accessKeySecret:
                    description: AccessKeySecret is the secret containing S3 access
                      key
                    type: string
                  bucket:
                    description: Bucket is the S3 bucket name
                    type: string
                  endpoint:
                    description: Endpoint is the S3 endpoint URL
                    type: string
                  path:
                    description: Path is the path prefix in the bucket
                    type: string
                  region:
                    description: Region is the S3 region
                    type: string
                  secretKeySecret:
                    description: SecretKeySecret is the secret containing S3 secret
                      key
                    type: string
                required:
                - bucket
                type: object
              schedule:
                description: Schedule is the cron schedule for automatic backups
                type: string
              storageType:
                description: StorageType is the type of storage backend
                type: string
            required:
            - clusterName
            - storageType
            type: object
          status:
            description: EtcdBackupStatus defines the observed state of EtcdBackup
            properties:
              backupSize:
                description: BackupSize is the size of the backup in bytes
                format: int64
                type: integer
              completionTime:
                description: CompletionTime is the time when the backup completed
                format: date-time
                type: string
              conditions:
                description: Conditions represent the latest available observations
                  of the backup's state
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource.\n---\nThis struct is intended for
                    direct use as an array at the field path .status.conditions.  For
                    example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the
                    observations of a foo's current state.\n\t    // Known .status.conditions.type
                    are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    //
                    +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t
                    \   // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\"
                    patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t
                    \   // other fields\n\t}"
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: |-
                        type of condition in CamelCase or in foo.example.com/CamelCase.
                        ---
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
                        useful (see .node.status.conditions), the ability to deconflict is important.
                        The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              etcdRevision:
                description: EtcdRevision is the etcd revision that was backed up
                format: int64
                type: integer
              etcdVersion:
                description: EtcdVersion is the version of etcd that was backed up
                type: string
              phase:
                description: Phase is the current phase of the backup
                type: string
              startTime:
                description: StartTime is the time when the backup started
                format: date-time
                type: string
              storagePath:
                description: StoragePath is the path where the backup is stored
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: etcdclusters.etcd.etcd.io
spec:
  group: etcd.etcd.io
  names:
    kind: EtcdCluster
    listKind: EtcdClusterList
    plural: etcdclusters
    shortNames:
    - etcd
    singular: etcdcluster
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .spec.size
      name: Size
      type: integer
    - jsonPath: .status.readyReplicas
      name: Ready
      type: integer
    - jsonPath: .spec.version
      name: Version
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdCluster is the Schema for the etcdclusters API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: EtcdClusterSpec defines the desired state of EtcdCluster
            properties:
              repository:
                default: quay.io/coreos/etcd
                description: Repository is the container image repository
                type: string
              resources:
                description: Resources configuration
                properties:
                  limits:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    description: Limits describes the maximum amount of compute resources
                      allowed
                    type: object
                  requests:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    description: Requests describes the minimum amount of compute
                      resources required
                    type: object
                type: object
              security:
                description: Security configuration
                properties:
                  tls:
                    description: TLS configuration
                    properties:
                      autoTLS:
                        default: true
                        description: AutoTLS indicates whether to automatically generate
                          TLS certificates
                        type: boolean
                      caSecret:
                        description: CASecret is the name of the secret containing
                          the CA certificate
                        type: string
                      certificateSecret:
                        description: CertificateSecret is the name of the secret containing
                          TLS certificates
                        type: string
                      clientTLSEnabled:
                        default: true
                        description: ClientTLSEnabled indicates whether client TLS
                          is enabled
                        type: boolean
                      enabled:
                        default: true
                        description: Enabled indicates whether TLS is enabled
                        type: boolean
                      peerTLSEnabled:
                        default: true
                        description: PeerTLSEnabled indicates whether peer TLS is
                          enabled
                        type: boolean
                    type: object
                type: object
              size:
                default: 3
                description: Size is the number of etcd members in the cluster
                format: int32
                maximum: 9
                minimum: 0
                type: integer
              storage:
                description: Storage configuration
                properties:
                  size:
                    anyOf:
                    - type: integer
                    - type: string
                    default: 10Gi
                    description: Size is the size of the storage volume
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  storageClassName:
                    description: StorageClassName is the name of the StorageClass
                      to use for etcd data
                    type: string
                  volumeClaimTemplate:
                    description: VolumeClaimTemplate allows customizing the PVC template
                    properties:
                      metadata:
                        description: |-
                          May contain labels and annotations that will be copied into the PVC
                          when creating it. No other fields are allowed and will be rejected during
                          validation.
                        type: object
                      spec:
                        description: |-
                          The specification for the PersistentVolumeClaim. The entire content is
                          copied unchanged into the PVC that gets created from this
                          template. The same fields as in a PersistentVolumeClaim
                          are also valid here.
                        properties:
                          accessModes:
                            description: |-
                              accessModes contains the desired access modes the volume should have.
                              More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                            items:
                              type: string
                            type: array
                            x-kubernetes-list-type: atomic
                          dataSource:
                            description: |-
                              dataSource field can be used to specify either:
                              * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)
                              * An existing PVC (PersistentVolumeClaim)
                              If the provisioner or an external controller can support the specified data source,
                              it will create a new volume based on the contents of the specified data source.
                              When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,
                              and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.
                              If the namespace is specified, then dataSourceRef will not be copied to dataSource.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup is the group for the resource being referenced.
                                  If APIGroup is not specified, the specified Kind must be in the core API group.
                                  For any other third-party types, APIGroup is required.
                                type: string
                              kind:
                                description: Kind is the type of resource being referenced
                                type: string
                              name:
                                description: Name is the name of resource being referenced
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                            x-kubernetes-map-type: atomic
                          dataSourceRef:
                            description: |-
                              dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
                              volume is desired. This may be any object from a non-empty API group (non
                              core object) or a PersistentVolumeClaim object.
                              When this field is specified, volume binding will only succeed if the type of
                              the specified object matches some installed volume populator or dynamic
                              provisioner.
                              This field will replace the functionality of the dataSource field and as such
                              if both fields are non-empty, they must have the same value. For backwards
                              compatibility, when namespace isn't specified in dataSourceRef,
                              both fields (dataSource and dataSourceRef) will be set to the same
                              value automatically if one of them is empty and the other is non-empty.
                              When namespace is specified in dataSourceRef,
                              dataSource isn't set to the same value and must be empty.
                              There are three important differences between dataSource and dataSourceRef:
                              * While dataSource only allows two specific types of objects, dataSourceRef
                                allows any non-core object, as well as PersistentVolumeClaim objects.
                              * While dataSource ignores disallowed values (dropping them), dataSourceRef
                                preserves all values, and generates an error if a disallowed value is
                                specified.
                              * While dataSource only allows local objects, dataSourceRef allows objects
                                in any namespaces.
                              (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.
                              (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                            properties:
                              apiGroup:
                                description: |-
                                  APIGroup is the group for the resource being referenced.
                                  If APIGroup is not specified, the specified Kind must be in the core API group.
                                  For any other third-party types, APIGroup is required.
                                type: string
                              kind:
                                description: Kind is the type of resource being referenced
                                type: string
                              name:
                                description: Name is the name of resource being referenced
                                type: string
                              namespace:
                                description: |-
                                  Namespace is the namespace of resource being referenced
                                  Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.
                                  (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                                type: string
                            required:
                            - kind
                            - name
                            type: object
                          resources:
                            description: |-
                              resources represents the minimum resources the volume should have.
                              If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements
                              that are lower than previous value but must still be higher than capacity recorded in the
                              status field of the claim.
                              More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources
                            properties:
                              limits:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                description: |-
                                  Limits describes the maximum amount of compute resources allowed.
                                  More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                type: object
                              requests:
                                additionalProperties:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                  x-kubernetes-int-or-string: true
                                description: |-
                                  Requests describes the minimum amount of compute resources required.
                                  If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                  otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                  More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                type: object
                            type: object
                          selector:
                            description: selector is a label query over volumes to
                              consider for binding.
                            properties:
                              matchExpressions:
                                description: matchExpressions is a list of label selector
                                  requirements. The requirements are ANDed.
                                items:
                                  description: |-
                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                    relates the key and values.
                                  properties:
                                    key:
                                      description: key is the label key that the selector
                                        applies to.
                                      type: string
                                    operator:
                                      description: |-
                                        operator represents a key's relationship to a set of values.
                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                      type: string
                                    values:
                                      description: |-
                                        values is an array of string values. If the operator is In or NotIn,
                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                        the values array must be empty. This array is replaced during a strategic
                                        merge patch.
                                      items:
                                        type: string
                                      type: array
                                      x-kubernetes-list-type: atomic
                                  required:
                                  - key
                                  - operator
                                  type: object
                                type: array
                                x-kubernetes-list-type: atomic
                              matchLabels:
                                additionalProperties:
                                  type: string
                                description: |-
                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                type: object
                            type: object
                            x-kubernetes-map-type: atomic
                          storageClassName:
                            description: |-
                              storageClassName is the name of the StorageClass required by the claim.
                              More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
                            type: string
                          volumeAttributesClassName:
                            description: |-
                              volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.
                              If specified, the CSI driver will create or update the volume with the attributes defined
                              in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,
                              it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass
                              will be applied to the claim but it's not allowed to reset this field to empty string once it is set.
                              If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass
                              will be set by the persistentvolume controller if it exists.
                              If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be
                              set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource
                              exists.
                              More info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/
                              (Alpha) Using this field requires the VolumeAttributesClass feature gate to be enabled.
                            type: string
                          volumeMode:
                            description: |-
                              volumeMode defines what type of volume is required by the claim.
                              Value of Filesystem is implied when not included in claim spec.
                            type: string
                          volumeName:
                            description: volumeName is the binding reference to the
                              PersistentVolume backing this claim.
                            type: string
                        type: object
                    required:
                    - spec
                    type: object
                type: object
              version:
                default: v3.5.21
                description: Version is the etcd version to use (supports both "3.5.21"
                  and "v3.5.21" formats)
                pattern: ^v?3\.[0-9]+\.[0-9]+$
                type: string
            type: object
          status:
            description: EtcdClusterStatus defines the observed state of EtcdCluster
            properties:
              clientEndpoints:
                description: ClientEndpoints are the client endpoints of the etcd
                  cluster
                items:
                  type: string
                type: array
              clusterID:
                description: ClusterID is the etcd cluster ID
                type: string
              conditions:
                description: Conditions represent the latest available observations
                  of the cluster's state
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource.\n---\nThis struct is intended for
                    direct use as an array at the field path .status.conditions.  For
                    example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the
                    observations of a foo's current state.\n\t    // Known .status.conditions.type
                    are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    //
                    +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t
                    \   // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\"
                    patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t
                    \   // other fields\n\t}"
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: |-
                        type of condition in CamelCase or in foo.example.com/CamelCase.
                        ---
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
                        useful (see .node.status.conditions), the ability to deconflict is important.
                        The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastBackupTime:
                description: LastBackupTime is the time of the last successful backup
                format: date-time
                type: string
              lastUpdateTime:
                description: LastUpdateTime is the last time the status was updated
                format: date-time
                type: string
              leaderID:
                description: LeaderID is the ID of the current etcd leader
                type: string
              members:
                description: Members is the list of etcd cluster members
                items:
                  description: EtcdMember represents an etcd cluster member
                  properties:
                    clientURL:
                      description: ClientURL is the client URL of the etcd member
                      type: string
                    id:
                      description: ID is the etcd member ID
                      type: string
                    name:
                      description: Name is the name of the etcd member
                      type: string
                    peerURL:
                      description: PeerURL is the peer URL of the etcd member
                      type: string
                    ready:
                      description: Ready indicates if the member is ready
                      type: boolean
                    role:
                      description: Role indicates the role of the member (leader/follower)
                      type: string
                  type: object
                type: array
              observedGeneration:
                description: ObservedGeneration is the most recent generation observed
                  by the controller
                format: int64
                type: integer
              phase:
                description: Phase is the current phase of the etcd cluster
                type: string
              readyReplicas:
                description: ReadyReplicas is the number of ready etcd replicas
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.15.0
  name: etcdrestores.etcd.etcd.io
spec:
  group: etcd.etcd.io
  names:
    kind: EtcdRestore
    listKind: EtcdRestoreList
    plural: etcdrestores
    shortNames:
    - etcdrestore
    singular: etcdrestore
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .spec.backupName
      name: Backup
      type: string
    - jsonPath: .spec.clusterName
      name: Cluster
      type: string
    - jsonPath: .spec.restoreType
      name: Type
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdRestore is the Schema for the etcdrestores API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: EtcdRestoreSpec defines the desired state of EtcdRestore
            properties:
              backupName:
                description: BackupName is the name of the EtcdBackup to restore from
                type: string
              backupNamespace:
                description: BackupNamespace is the namespace of the EtcdBackup
                type: string
              clusterName:
                description: ClusterName is the name of the target EtcdCluster
                type: string
              clusterTemplate:
                description: ClusterTemplate is the template for creating a new cluster
                  (for new restore type)
                properties:
                  repository:
                    default: quay.io/coreos/etcd
                    description: Repository is the container image repository
                    type: string
                  resources:
                    description: Resources configuration
                    properties:
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: Limits describes the maximum amount of compute
                          resources allowed
                        type: object
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: Requests describes the minimum amount of compute
                          resources required
                        type: object
                    type: object
                  security:
                    description: Security configuration
                    properties:
                      tls:
                        description: TLS configuration
                        properties:
                          autoTLS:
                            default: true
                            description: AutoTLS indicates whether to automatically
                              generate TLS certificates
                            type: boolean
                          caSecret:
                            description: CASecret is the name of the secret containing
                              the CA certificate
                            type: string
                          certificateSecret:
                            description: CertificateSecret is the name of the secret
                              containing TLS certificates
                            type: string
                          clientTLSEnabled:
                            default: true
                            description: ClientTLSEnabled indicates whether client
                              TLS is enabled
                            type: boolean
                          enabled:
                            default: true
                            description: Enabled indicates whether TLS is enabled
                            type: boolean
                          peerTLSEnabled:
                            default: true
                            description: PeerTLSEnabled indicates whether peer TLS
                              is enabled
                            type: boolean
                        type: object
                    type: object
                  size:
                    default: 3
                    description: Size is the number of etcd members in the cluster
                    format: int32
                    maximum: 9
                    minimum: 0
                    type: integer
                  storage:
                    description: Storage configuration
                    properties:
                      size:
                        anyOf:
                        - type: integer
                        - type: string
                        default: 10Gi
                        description: Size is the size of the storage volume
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      storageClassName:
                        description: StorageClassName is the name of the StorageClass
                          to use for etcd data
                        type: string
                      volumeClaimTemplate:
                        description: VolumeClaimTemplate allows customizing the PVC
                          template
                        properties:
                          metadata:
                            description: |-
                              May contain labels and annotations that will be copied into the PVC
                              when creating it. No other fields are allowed and will be rejected during
                              validation.
                            type: object
                          spec:
                            description: |-
                              The specification for the PersistentVolumeClaim. The entire content is
                              copied unchanged into the PVC that gets created from this
                              template. The same fields as in a PersistentVolumeClaim
                              are also valid here.
                            properties:
                              accessModes:
                                description: |-
                                  accessModes contains the desired access modes the volume should have.
                                  More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                              dataSource:
                                description: |-
                                  dataSource field can be used to specify either:
                                  * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)
                                  * An existing PVC (PersistentVolumeClaim)
                                  If the provisioner or an external controller can support the specified data source,
                                  it will create a new volume based on the contents of the specified data source.
                                  When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,
                                  and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.
                                  If the namespace is specified, then dataSourceRef will not be copied to dataSource.
                                properties:
                                  apiGroup:
                                    description: |-
                                      APIGroup is the group for the resource being referenced.
                                      If APIGroup is not specified, the specified Kind must be in the core API group.
                                      For any other third-party types, APIGroup is required.
                                    type: string
                                  kind:
                                    description: Kind is the type of resource being
                                      referenced
                                    type: string
                                  name:
                                    description: Name is the name of resource being
                                      referenced
                                    type: string
                                required:
                                - kind
                                - name
                                type: object
                                x-kubernetes-map-type: atomic
                              dataSourceRef:
                                description: |-
                                  dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
                                  volume is desired. This may be any object from a non-empty API group (non
                                  core object) or a PersistentVolumeClaim object.
                                  When this field is specified, volume binding will only succeed if the type of
                                  the specified object matches some installed volume populator or dynamic
                                  provisioner.
                                  This field will replace the functionality of the dataSource field and as such
                                  if both fields are non-empty, they must have the same value. For backwards
                                  compatibility, when namespace isn't specified in dataSourceRef,
                                  both fields (dataSource and dataSourceRef) will be set to the same
                                  value automatically if one of them is empty and the other is non-empty.
                                  When namespace is specified in dataSourceRef,
                                  dataSource isn't set to the same value and must be empty.
                                  There are three important differences between dataSource and dataSourceRef:
                                  * While dataSource only allows two specific types of objects, dataSourceRef
                                    allows any non-core object, as well as PersistentVolumeClaim objects.
                                  * While dataSource ignores disallowed values (dropping them), dataSourceRef
                                    preserves all values, and generates an error if a disallowed value is
                                    specified.
                                  * While dataSource only allows local objects, dataSourceRef allows objects
                                    in any namespaces.
                                  (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.
                                  (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                                properties:
                                  apiGroup:
                                    description: |-
                                      APIGroup is the group for the resource being referenced.
                                      If APIGroup is not specified, the specified Kind must be in the core API group.
                                      For any other third-party types, APIGroup is required.
                                    type: string
                                  kind:
                                    description: Kind is the type of resource being
                                      referenced
                                    type: string
                                  name:
                                    description: Name is the name of resource being
                                      referenced
                                    type: string
                                  namespace:
                                    description: |-
                                      Namespace is the namespace of resource being referenced
                                      Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.
                                      (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                                    type: string
                                required:
                                - kind
                                - name
                                type: object
                              resources:
                                description: |-
                                  resources represents the minimum resources the volume should have.
                                  If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements
                                  that are lower than previous value but must still be higher than capacity recorded in the
                                  status field of the claim.
                                  More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources
                                properties:
                                  limits:
                                    additionalProperties:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                      x-kubernetes-int-or-string: true
                                    description: |-
                                      Limits describes the maximum amount of compute resources allowed.
                                      More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                    type: object
                                  requests:
                                    additionalProperties:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                      x-kubernetes-int-or-string: true
                                    description: |-
                                      Requests describes the minimum amount of compute resources required.
                                      If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                      otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                      More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                    type: object
                                type: object
                              selector:
                                description: selector is a label query over volumes
                                  to consider for binding.
                                properties:
                                  matchExpressions:
                                    description: matchExpressions is a list of label
                                      selector requirements. The requirements are
                                      ANDed.
                                    items:
                                      description: |-
                                        A label selector requirement is a selector that contains values, a key, and an operator that
                                        relates the key and values.
                                      properties:
                                        key:
                                          description: key is the label key that the
                                            selector applies to.
                                          type: string
                                        operator:
                                          description: |-
                                            operator represents a key's relationship to a set of values.
                                            Valid operators are In, NotIn, Exists and DoesNotExist.
                                          type: string
                                        values:
                                          description: |-
                                            values is an array of string values. If the operator is In or NotIn,
                                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                            the values array must be empty. This array is replaced during a strategic
                                            merge patch.
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                      required:
                                      - key
                                      - operator
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  matchLabels:
                                    additionalProperties:
                                      type: string
                                    description: |-
                                      matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                      map is equivalent to an element of matchExpressions, whose key field is "key", the
                                      operator is "In", and the values array contains only "value". The requirements are ANDed.
                                    type: object
                                type: object
                                x-kubernetes-map-type: atomic
                              storageClassName:
                                description: |-
                                  storageClassName is the name of the StorageClass required by the claim.
                                  More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
                                type: string
                              volumeAttributesClassName:
                                description: |-
                                  volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.
                                  If specified, the CSI driver will create or update the volume with the attributes defined
                                  in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,
                                  it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass
                                  will be applied to the claim but it's not allowed to reset this field to empty string once it is set.
                                  If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass
                                  will be set by the persistentvolume controller if it exists.
                                  If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be
                                  set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource
                                  exists.
                                  More info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/
                                  (Alpha) Using this field requires the VolumeAttributesClass feature gate to be enabled.
                                type: string
                              volumeMode:
                                description: |-
                                  volumeMode defines what type of volume is required by the claim.
                                  Value of Filesystem is implied when not included in claim spec.
                                type: string
                              volumeName:
                                description: volumeName is the binding reference to
                                  the PersistentVolume backing this claim.
                                type: string
                            type: object
                        required:
                        - spec
                        type: object
                    type: object
                  version:
                    default: v3.5.21
                    description: Version is the etcd version to use (supports both
                      "3.5.21" and "v3.5.21" formats)
                    pattern: ^v?3\.[0-9]+\.[0-9]+$
                    type: string
                type: object
              dataDir:
                description: DataDir is the data directory for etcd
                type: string
              restoreType:
                description: RestoreType is the type of restore operation
                type: string
              skipHashCheck:
                description: SkipHashCheck skips hash check during restore
                type: boolean
              walDir:
                description: WalDir is the WAL directory for etcd
                type: string
            required:
            - backupName
            - clusterName
            type: object
          status:
            description: EtcdRestoreStatus defines the observed state of EtcdRestore
            properties:
              completionTime:
                description: CompletionTime is the time when the restore completed
                format: date-time
                type: string
              conditions:
                description: Conditions represent the latest available observations
                  of the restore's state
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource.\n---\nThis struct is intended for
                    direct use as an array at the field path .status.conditions.  For
                    example,\n\n\n\ttype FooStatus struct{\n\t    // Represents the
                    observations of a foo's current state.\n\t    // Known .status.conditions.type
                    are: \"Available\", \"Progressing\", and \"Degraded\"\n\t    //
                    +patchMergeKey=type\n\t    // +patchStrategy=merge\n\t    // +listType=map\n\t
                    \   // +listMapKey=type\n\t    Conditions []metav1.Condition `json:\"conditions,omitempty\"
                    patchStrategy:\"merge\" patchMergeKey:\"type\" protobuf:\"bytes,1,rep,name=conditions\"`\n\n\n\t
                    \   // other fields\n\t}"
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: |-
                        type of condition in CamelCase or in foo.example.com/CamelCase.
                        ---
                        Many .condition.type values are consistent across resources like Available, but because arbitrary conditions can be
                        useful (see .node.status.conditions), the ability to deconflict is important.
                        The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              phase:
                description: Phase is the current phase of the restore
                type: string
              restoredCluster:
                description: RestoredCluster is the name of the restored cluster
                type: string
              restoredSize:
                description: RestoredSize is the size of the restored data in bytes
                format: int64
                type: integer
              startTime:
                description: StartTime is the time when the restore started
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-controller-manager
  namespace: etcd-k8s-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-leader-election-role
  namespace: etcd-k8s-operator-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdbackup-editor-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdbackup-viewer-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdcluster-editor-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdcluster-viewer-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdrestore-editor-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-etcdrestore-viewer-role
rules:
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: etcd-k8s-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdbackups/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/finalizers
  verbs:
  - update
- apiGroups:
  - etcd.etcd.io
  resources:
  - etcdrestores/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-leader-election-rolebinding
  namespace: etcd-k8s-operator-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: etcd-k8s-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: etcd-k8s-operator-controller-manager
  namespace: etcd-k8s-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
  name: etcd-k8s-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: etcd-k8s-operator-manager-role
subjects:
- kind: ServiceAccount
  name: etcd-k8s-operator-controller-manager
  namespace: etcd-k8s-operator-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-k8s-operator
    control-plane: controller-manager
  name: etcd-k8s-operator-controller-manager
  namespace: etcd-k8s-operator-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --leader-elect
        - --health-probe-bind-address=:8081
        command:
        - /manager
        image: etcd-operator:dev-v1.0
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: etcd-k8s-operator-controller-manager
      terminationGracePeriodSeconds: 10
